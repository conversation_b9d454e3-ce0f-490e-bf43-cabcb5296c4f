import { Link } from "@tanstack/react-router";
import {
	ArrowLeft,
	Building,
	DollarSign,
	Hash,
	Package,
	Tag,
} from "lucide-react";
import { useService } from "src/config/context/serviceProvider";
import { AppRuntime } from "src/core/service/utils/runtimes";
import useCreateProductionDevicePage from "./use-create-production-device-page";

export default function CreateProductionDevicePage() {
	const { product } = useService();
	const { form, handleSubmit, isPending, brands, measurementUnits } =
		useCreateProductionDevicePage();

	return (
		<div className="container mx-auto max-w-4xl">
			<div className="mb-6">
				<div className="mb-4 flex items-center gap-4">
					<Link
						to="/admin/products/production-devices"
						className="btn btn-ghost btn-sm"
					>
						<ArrowLeft size={16} />
						Volver
					</Link>
					<h1 className="font-bold text-2xl">
						Crear Dispositivo de Producción
					</h1>
				</div>
			</div>

			<div className="space-y-6">
				<div className="card bg-base-100 shadow-sm">
					<div className="card-body">
						<h2 className="card-title">
							Información del Dispositivo de Producción
						</h2>
						<form.AppForm>
							<fieldset className="fieldset">
								<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
									<form.AppField
										name="name"
										children={({ FSTextField }) => (
											<FSTextField
												label="Nombre"
												placeholder="Nombre del dispositivo"
												prefixComponent={<Tag size={16} />}
											/>
										)}
									/>
									<form.AppField
										name="commercialName"
										children={({ FSTextField }) => (
											<FSTextField
												label="Nombre Comercial"
												placeholder="Nombre comercial del dispositivo"
												prefixComponent={<Building size={16} />}
											/>
										)}
									/>
								</div>

								<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
									<form.AppField
										name="code"
										validators={{
											onChangeAsyncDebounceMs: 500,
											onChangeAsync: async ({ value }) => {
												if (!value || value.trim() === "") {
													return undefined;
												}
												try {
													await AppRuntime.runPromise(
														product.validateCode(value),
													);
													return undefined;
												} catch (e) {
													return [{ message: "El código ya existe" }];
												}
											},
										}}
										children={({ FSTextField }) => (
											<FSTextField
												label="Código"
												placeholder="Código del dispositivo"
												prefixComponent={<Hash size={16} />}
											/>
										)}
									/>
									<form.AppField
										name="skuCode"
										validators={{
											onChangeAsyncDebounceMs: 500,
											onChangeAsync: async ({ value }) => {
												if (!value || value.trim() === "") {
													return undefined;
												}
												try {
													await AppRuntime.runPromise(
														product.validateSKUCode(value),
													);
													return undefined;
												} catch (e) {
													return [{ message: "El código SKU ya existe" }];
												}
											},
										}}
										children={({ FSTextField }) => (
											<FSTextField
												label="Código SKU"
												placeholder="Código SKU del dispositivo"
												prefixComponent={<Package size={16} />}
											/>
										)}
									/>
								</div>

								<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
									<form.AppField
										name="brandID"
										children={({ FSSelectField }) => (
											<FSSelectField
												label="Marca"
												placeholder="Seleccionar marca"
												options={brands.map((brand) => ({
													value: brand.id,
													label: brand.name,
												}))}
											/>
										)}
									/>
									<form.AppField
										name="measurementUnitID"
										children={({ FSSelectField }) => (
											<FSSelectField
												label="Unidad de Medida"
												placeholder="Seleccionar unidad"
												options={measurementUnits.map((unit) => ({
													value: unit.id,
													label: unit.name,
												}))}
											/>
										)}
									/>
								</div>

								<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
									<form.AppField
										name="costPrice"
										children={({ FSTextField }) => (
											<FSTextField
												label="Precio de Costo"
												placeholder="0.00"
												type="number"
												prefixComponent={<DollarSign size={16} />}
											/>
										)}
									/>
									<form.AppField
										name="state"
										children={({ FSSelectField }) => (
											<FSSelectField
												label="Estado"
												placeholder="Seleccionar estado"
												options={[
													{ value: "ACTIVE", label: "Activo" },
													{ value: "INACTIVE", label: "Inactivo" },
												]}
											/>
										)}
									/>
								</div>

								<form.AppField
									name="description"
									children={({ FSTextArea }) => (
										<FSTextArea
											label="Descripción"
											placeholder="Descripción del dispositivo (opcional)"
										/>
									)}
								/>

								<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
									<form.AppField
										name="canBeSold"
										children={({ FSToggleField }) => (
											<FSToggleField label="Se puede vender" />
										)}
									/>
									<form.AppField
										name="canBePurchased"
										children={({ FSToggleField }) => (
											<FSToggleField label="Se puede comprar" />
										)}
									/>
								</div>
							</fieldset>
						</form.AppForm>
					</div>
				</div>

				{/* Submit Button */}
				<div className="card bg-base-100 shadow-sm">
					<div className="card-body">
						<div className="flex justify-end gap-4">
							<Link
								to="/admin/products/production-devices"
								className="btn btn-ghost"
							>
								Cancelar
							</Link>
							<button
								type="button"
								className="btn btn-primary"
								onClick={handleSubmit}
								disabled={isPending}
							>
								{isPending ? (
									<span className="loading loading-spinner loading-sm" />
								) : (
									"Crear Dispositivo"
								)}
							</button>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
